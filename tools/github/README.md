# GitHub Inactive Users Finder

A Python application to identify inactive users in a GitHub organization. This tool helps organization administrators find members who haven't had any activity in the last 3 months.

## Features

- 🔍 **Comprehensive Activity Detection**: Checks both user events and commit activity
- 📊 **Detailed Reporting**: Generates formatted reports with user information
- 👤 **Rich User Details**: Includes full names, emails, company, and location information
- 📅 **Actual Last Activity**: Shows real last activity dates for inactive users (not just "Unknown")
- 📄 **Multiple Export Formats**: Text reports and CSV exports for easy data handling
- 🚀 **Easy to Use**: Simple command-line interface
- ⚡ **Efficient**: Uses pagination to handle large organizations
- 🔒 **Secure**: Uses GitHub personal access tokens for authentication

## What Counts as Activity?

The tool considers a user "active" if they have performed any of the following actions within the specified time period (default: 90 days):

- Created issues or pull requests
- Commented on issues or pull requests
- Pushed commits to any repository
- Starred or watched repositories
- Created or deleted repositories
- Any other GitHub events tracked by the Events API

## Prerequisites

- Python 3.7 or higher
- A GitHub personal access token with appropriate permissions

## Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up your GitHub token**:
   
   Create a GitHub personal access token:
   - Go to [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
   - Click "Generate new token (classic)"
   - Select the following scopes:
     - `read:org` - to read organization membership
     - `read:user` - to read user information
   - Copy the generated token

3. **Configure environment variables**:
   ```bash
   cp ../../.env.example .env
   # Edit .env and add your GitHub token
   ```

## Usage

### Basic Usage

```bash
python github_inactive_users.py --org YOUR_ORG_NAME
```

### With Custom Date Format and Timezone

```bash
# Use custom date format and timezone
python github_inactive_users.py --org YOUR_ORG_NAME --date-format "%m/%d/%Y %I:%M %p" --timezone "US/Eastern"

# European format with local timezone
python github_inactive_users.py --org YOUR_ORG_NAME --date-format "%d.%m.%Y %H:%M" --timezone "Europe/Berlin"
```

### Save Report to File

```bash
# Save text report
python github_inactive_users.py --org YOUR_ORG_NAME --days 90 --output inactive_users_report.txt

# Export to CSV for spreadsheet analysis
python github_inactive_users.py --org YOUR_ORG_NAME --days 90 --csv inactive_users.csv
```

### Command Line Options

- `--org` (required): GitHub organization name
- `--days` (optional): Number of days to consider a user inactive (default: 90)
- `--token` (optional): GitHub personal access token (can also be set via GITHUB_TOKEN environment variable)
- `--output` (optional): Output file path for text report
- `--csv` (optional): Output file path for CSV export
- `--date-format` (optional): Date format for displaying dates (default: "%m/%d/%Y %I:%M:%S %p")
- `--timezone` (optional): Timezone for date display (default: "US/Pacific")

## Rate Limiting

The GitHub API has rate limits:
- **Authenticated requests**: 5,000 requests per hour
- **Search API**: 30 requests per minute

The script automatically handles these rate limits using a conservative batch processing approach:
- **Batch Processing**: Processes users in batches of 15 (reduced from 20 due to additional API calls for fetching actual last activity dates)
- **Automatic Cooldown**: Waits 60 seconds between batches with a countdown timer
- **Rate Limit Prevention**: This approach prevents 403 rate limit errors
- **Progress Indicators**: Shows batch progress and countdown timers

## Security Notes

- Never commit your GitHub token to version control
- Use environment variables or the `.env` file to store sensitive information
- Consider using a token with minimal required permissions
- Regularly rotate your access tokens

#!/usr/bin/env python3
"""
Example usage of the Atlassian Inactive Users Finder

This script demonstrates how to use the AtlassianInactiveUsersFinder class programmatically.
"""

import os
import sys

# Add the current directory to the path to import the module
sys.path.append(os.path.dirname(__file__))
from atlassian_inactive_users import AtlassianInactiveUsersFinder

def main():
    # Example usage - replace with your actual values
    JIRA_URL = 'https://yourcompany.atlassian.net'
    CONFLUENCE_URL = 'https://yourcompany.atlassian.net/wiki'  # Optional, will default to jira_url/wiki
    ATLASSIAN_EMAIL = os.getenv('ATLASSIAN_EMAIL', '<EMAIL>')
    ATLASSIAN_API_TOKEN = os.getenv('ATLASSIAN_API_TOKEN', 'your_api_token_here')
    
    if ATLASSIAN_EMAIL == '<EMAIL>' or ATLASSIAN_API_TOKEN == 'your_api_token_here':
        print("Please set your ATLASSIAN_EMAIL and ATLASSIAN_API_TOKEN environment variables or update this script")
        return
    
    try:
        # Initialize the finder with custom settings
        INACTIVE_DAYS = 60  # Customize this value
        DATE_FORMAT = "%Y-%m-%d %H:%M:%S"  # ISO format
        TIMEZONE = "UTC"  # UTC time
        
        print(f"Initializing Atlassian Inactive Users Finder")
        print(f"Jira URL: {JIRA_URL}")
        print(f"Confluence URL: {CONFLUENCE_URL}")
        print(f"Looking for users inactive for {INACTIVE_DAYS} days or more")
        print(f"Date format: {DATE_FORMAT}")
        print(f"Timezone: {TIMEZONE}")
        
        finder = AtlassianInactiveUsersFinder(
            JIRA_URL, CONFLUENCE_URL, ATLASSIAN_EMAIL, ATLASSIAN_API_TOKEN,
            INACTIVE_DAYS, DATE_FORMAT, TIMEZONE
        )

        # Get unified user list
        users = finder.get_unified_user_list()
        print(f"Found {len(users)} unique users across both platforms")

        # Find active users
        print("\nFinding active users...")
        active_users = finder.find_active_users()

        # Generate and display report
        report = finder.generate_report(active_users)
        print(report)

        # Optionally save to file
        org_name = JIRA_URL.split('//')[1].split('.')[0] if '//' in JIRA_URL else "atlassian"
        report_filename = f'{org_name}_active_users_report.txt'
        with open(report_filename, 'w') as f:
            f.write(report)
        print(f"Report saved to {report_filename}")

        # Optionally export to CSV
        csv_filename = f'{org_name}_active_users.csv'
        finder.export_to_csv(active_users, csv_filename)
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Atlassian (Jira + Confluence) Inactive Users Finder

This script identifies inactive users across Jira and Confluence instances.
Inactive users are defined as users who haven't had any activity in the last 3 months.
"""

import os
import sys
import requests
import json
import time
import csv
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Set
import argparse
from dotenv import load_dotenv

# Add the shared directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from shared.base_audit_tool import BaseAuditTool

# Load environment variables
load_dotenv()


class AtlassianInactiveUsersFinder(BaseAuditTool):
    def __init__(self, jira_url: str, confluence_url: str, email: str, api_token: str,
                 inactive_days: int = 90, date_format: str = "%m/%d/%Y %I:%M:%S %p", 
                 timezone: str = "US/Pacific"):
        """
        Initialize the Atlassian API client.

        Args:
            jira_url: Jira instance URL (e.g., "https://yourcompany.atlassian.net")
            confluence_url: Confluence instance URL (e.g., "https://yourcompany.atlassian.net/wiki")
            email: Atlassian account email
            api_token: Atlassian API token
            inactive_days: Number of days to consider a user inactive (default: 90)
            date_format: Date format string for displaying dates
            timezone: Timezone for date display
        """
        super().__init__(inactive_days, date_format, timezone)
        
        self.jira_url = jira_url.rstrip('/')
        self.confluence_url = confluence_url.rstrip('/')
        self.email = email
        self.api_token = api_token
        
        # Set up authentication
        self.auth = (email, api_token)
        
        # Common headers
        self.headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        self.session = requests.Session()
        self.session.auth = self.auth
        self.session.headers.update(self.headers)

    def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """
        Make a request to the Atlassian API with error handling.

        Args:
            url: API endpoint URL
            params: Query parameters

        Returns:
            JSON response or None if error
        """
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            return None

    def _get_paginated_results(self, url: str, params: Optional[Dict] = None, 
                              results_key: str = "values") -> List[Dict]:
        """
        Get all results from a paginated Atlassian API endpoint.

        Args:
            url: API endpoint URL
            params: Query parameters
            results_key: Key in response containing the results array

        Returns:
            List of all results across all pages
        """
        all_results = []
        start_at = 0
        max_results = 50

        if params is None:
            params = {}

        while True:
            params.update({"startAt": start_at, "maxResults": max_results})
            response = self._make_request(url, params)
            
            if not response:
                break

            results = response.get(results_key, [])
            if not results:
                break

            all_results.extend(results)

            # Check if there are more pages
            if len(results) < max_results or response.get("isLast", True):
                break

            start_at += max_results

        return all_results

    def get_jira_users(self) -> List[Dict]:
        """
        Get all users from Jira.

        Returns:
            List of Jira users
        """
        print("Fetching users from Jira...")
        url = f"{self.jira_url}/rest/api/3/users/search"
        params = {"query": ""}  # Empty query returns all users
        users = self._get_paginated_results(url, params)
        print(f"Found {len(users)} users in Jira")
        return users

    def get_confluence_users(self) -> List[Dict]:
        """
        Get all users from Confluence.

        Returns:
            List of Confluence users
        """
        print("Fetching users from Confluence...")
        url = f"{self.confluence_url}/rest/api/user"
        users = self._get_paginated_results(url, results_key="results")
        print(f"Found {len(users)} users in Confluence")
        return users

    def get_unified_user_list(self) -> List[Dict]:
        """
        Get a unified list of users from both Jira and Confluence.

        Returns:
            List of unique users across both platforms
        """
        jira_users = self.get_jira_users()
        confluence_users = self.get_confluence_users()
        
        # Create a unified user list based on email/accountId
        unified_users = {}
        
        # Add Jira users
        for user in jira_users:
            account_id = user.get('accountId')
            email = user.get('emailAddress', '')
            if account_id:
                unified_users[account_id] = {
                    'accountId': account_id,
                    'displayName': user.get('displayName', 'N/A'),
                    'emailAddress': email,
                    'active': user.get('active', True),
                    'platforms': {'jira': True, 'confluence': False}
                }
        
        # Add/update with Confluence users
        for user in confluence_users:
            account_id = user.get('accountId')
            email = user.get('email', '')
            if account_id:
                if account_id in unified_users:
                    unified_users[account_id]['platforms']['confluence'] = True
                else:
                    unified_users[account_id] = {
                        'accountId': account_id,
                        'displayName': user.get('displayName', 'N/A'),
                        'emailAddress': email,
                        'active': True,  # Confluence API doesn't always provide this
                        'platforms': {'jira': False, 'confluence': True}
                    }
        
        user_list = list(unified_users.values())
        print(f"Found {len(user_list)} unique users across both platforms")
        return user_list

    def get_jira_user_activity(self, account_id: str) -> Dict:
        """
        Check user activity in Jira.

        Args:
            account_id: User's account ID

        Returns:
            Dictionary with activity information
        """
        activity_info = {
            "has_recent_activity": False,
            "last_activity_date": None,
            "activity_types": []
        }

        # Check recent issues created by user
        issues_url = f"{self.jira_url}/rest/api/3/search"
        since_date = self.cutoff_date.strftime("%Y-%m-%d")
        jql = f"reporter = '{account_id}' AND created >= '{since_date}'"
        
        issues_response = self._make_request(issues_url, {"jql": jql, "maxResults": 1})
        if issues_response and issues_response.get('total', 0) > 0:
            activity_info["has_recent_activity"] = True
            activity_info["activity_types"].append("IssueCreated")
            # Get the most recent issue
            latest_issue = issues_response['issues'][0]
            activity_info["last_activity_date"] = latest_issue['fields']['created']

        # Check recent comments by user
        # Note: This is a simplified check - in practice, you might want to check worklogs, 
        # issue updates, etc.
        
        return activity_info

    def get_confluence_user_activity(self, account_id: str) -> Dict:
        """
        Check user activity in Confluence.

        Args:
            account_id: User's account ID

        Returns:
            Dictionary with activity information
        """
        activity_info = {
            "has_recent_activity": False,
            "last_activity_date": None,
            "activity_types": []
        }

        # Check recent content created by user
        content_url = f"{self.confluence_url}/rest/api/content"
        since_date = self.cutoff_date.strftime("%Y-%m-%d")

        # Search for content created by the user since the cutoff date
        params = {
            "cql": f"creator = '{account_id}' AND created >= '{since_date}'",
            "limit": 1,
            "expand": "history"
        }

        content_response = self._make_request(content_url, params)
        if content_response and content_response.get('size', 0) > 0:
            activity_info["has_recent_activity"] = True
            activity_info["activity_types"].append("ContentCreated")
            # Get the most recent content
            latest_content = content_response['results'][0]
            activity_info["last_activity_date"] = latest_content['history']['createdDate']

        # Check recent content updates
        params = {
            "cql": f"lastModified >= '{since_date}' AND contributor = '{account_id}'",
            "limit": 1,
            "expand": "history"
        }

        updated_content_response = self._make_request(content_url, params)
        if updated_content_response and updated_content_response.get('size', 0) > 0:
            activity_info["has_recent_activity"] = True
            activity_info["activity_types"].append("ContentUpdated")
            latest_update = updated_content_response['results'][0]
            update_date = latest_update['history']['lastUpdated']['when']

            # Use the more recent date
            if not activity_info["last_activity_date"] or update_date > activity_info["last_activity_date"]:
                activity_info["last_activity_date"] = update_date

        return activity_info

    def get_user_activity(self, user: Dict) -> Dict:
        """
        Check user activity across both Jira and Confluence.

        Args:
            user: User dictionary with account information

        Returns:
            Dictionary with combined activity information
        """
        account_id = user['accountId']
        combined_activity = {
            "accountId": account_id,
            "has_recent_activity": False,
            "last_activity_date": None,
            "activity_types": [],
            "platforms_active": []
        }

        # Check Jira activity if user has access
        if user['platforms']['jira']:
            jira_activity = self.get_jira_user_activity(account_id)
            if jira_activity["has_recent_activity"]:
                combined_activity["has_recent_activity"] = True
                combined_activity["platforms_active"].append("Jira")
                combined_activity["activity_types"].extend(jira_activity["activity_types"])

                if not combined_activity["last_activity_date"] or \
                   (jira_activity["last_activity_date"] and
                    jira_activity["last_activity_date"] > combined_activity["last_activity_date"]):
                    combined_activity["last_activity_date"] = jira_activity["last_activity_date"]

        # Check Confluence activity if user has access
        if user['platforms']['confluence']:
            confluence_activity = self.get_confluence_user_activity(account_id)
            if confluence_activity["has_recent_activity"]:
                combined_activity["has_recent_activity"] = True
                combined_activity["platforms_active"].append("Confluence")
                combined_activity["activity_types"].extend(confluence_activity["activity_types"])

                if not combined_activity["last_activity_date"] or \
                   (confluence_activity["last_activity_date"] and
                    confluence_activity["last_activity_date"] > combined_activity["last_activity_date"]):
                    combined_activity["last_activity_date"] = confluence_activity["last_activity_date"]

        return combined_activity

    def find_inactive_users(self) -> List[Dict]:
        """
        Find all inactive users across Jira and Confluence.

        Returns:
            List of inactive users with their information
        """
        users = self.get_unified_user_list()
        inactive_users = []

        print(f"\nChecking activity for {len(users)} users...")
        print(f"Inactive period: {self.inactive_days} days")
        print(f"Cutoff date: {self.cutoff_date.strftime('%Y-%m-%d')}")
        print(f"Processing in batches of {self.batch_size} users with {self.batch_wait_time}s breaks to avoid rate limits")
        print("-" * 80)

        # Process users in batches to avoid rate limits
        for batch_start in range(0, len(users), self.batch_size):
            batch_end = min(batch_start + self.batch_size, len(users))
            batch_users = users[batch_start:batch_end]
            batch_num = (batch_start // self.batch_size) + 1
            total_batches = (len(users) + self.batch_size - 1) // self.batch_size

            print(f"\n📦 Processing batch {batch_num}/{total_batches} (users {batch_start + 1}-{batch_end})")

            for i, user in enumerate(batch_users):
                global_index = batch_start + i + 1
                display_name = user['displayName']
                print(f"[{global_index}/{len(users)}] Checking {display_name}...", end=" ")

                activity = self.get_user_activity(user)

                if not activity["has_recent_activity"]:
                    platforms = []
                    if user['platforms']['jira']:
                        platforms.append("Jira")
                    if user['platforms']['confluence']:
                        platforms.append("Confluence")

                    inactive_users.append({
                        "accountId": user['accountId'],
                        "displayName": user['displayName'],
                        "emailAddress": user['emailAddress'],
                        "platforms": " + ".join(platforms),
                        "last_activity": activity["last_activity_date"],
                        "active_status": user['active']
                    })

                    # Format last activity for console output
                    if activity["last_activity_date"]:
                        try:
                            formatted_date = self._format_date(activity["last_activity_date"])
                            time_ago = self._calculate_days_ago(activity["last_activity_date"])
                            print(f"❌ INACTIVE (last activity: {formatted_date}, {time_ago})")
                        except ValueError:
                            print("❌ INACTIVE")
                    else:
                        print("❌ INACTIVE")
                else:
                    # Format active user's last activity for console output
                    try:
                        formatted_date = self._format_date(activity['last_activity_date'])
                        time_ago = self._calculate_days_ago(activity['last_activity_date'])
                        platforms_active = " + ".join(activity["platforms_active"])
                        print(f"✅ Active on {platforms_active} (last activity: {formatted_date}, {time_ago})")
                    except (ValueError, TypeError):
                        platforms_active = " + ".join(activity["platforms_active"])
                        print(f"✅ Active on {platforms_active}")

            # Wait between batches (except for the last batch)
            if batch_end < len(users):
                print(f"\n⏸️  Batch {batch_num} complete. Rate limit cooldown...")
                self._countdown_timer(self.batch_wait_time, "⏳ Waiting to avoid rate limits")

        return inactive_users

    def generate_report(self, inactive_users: List[Dict]) -> str:
        """
        Generate a formatted report of inactive users.

        Args:
            inactive_users: List of inactive users

        Returns:
            Formatted report string
        """
        # Extract organization name from URLs
        org_name = self.jira_url.split('//')[1].split('.')[0] if '//' in self.jira_url else "Unknown"

        report = self.generate_base_report_header("Atlassian (Jira + Confluence)", org_name, len(inactive_users))

        if inactive_users:
            report += "Inactive Users:\n"
            report += "=" * 80 + "\n"
            for user in inactive_users:
                report += f"• {user['displayName']} ({user['platforms']})\n"
                report += f"  Email: {user['emailAddress']}\n"
                report += f"  Account ID: {user['accountId']}\n"
                report += f"  Account Status: {'Active' if user['active_status'] else 'Inactive'}\n"

                # Format the last activity date for better readability
                if user['last_activity']:
                    try:
                        formatted_date = self._format_date(user['last_activity'])
                        time_ago = self._calculate_days_ago(user['last_activity'])
                        report += f"  Last Activity: {formatted_date} ({time_ago})\n\n"
                    except ValueError:
                        # Fallback if date parsing fails
                        report += f"  Last Activity: {user['last_activity']}\n\n"
                else:
                    report += f"  Last Activity: Unknown\n\n"
        else:
            report += f"🎉 No inactive users found! All members have been active in the last {self.inactive_days} days.\n"

        return report

    def export_to_csv(self, inactive_users: List[Dict], filename: str) -> None:
        """
        Export inactive users to a CSV file.

        Args:
            inactive_users: List of inactive users
            filename: CSV file path
        """
        fieldnames = [
            'displayName', 'emailAddress', 'accountId', 'platforms',
            'active_status', 'last_activity'
        ]
        super().export_to_csv(inactive_users, filename, fieldnames)


def main():
    parser = argparse.ArgumentParser(description="Find inactive users in Atlassian (Jira + Confluence)")
    parser.add_argument("--jira-url", required=True, help="Jira instance URL (e.g., https://yourcompany.atlassian.net)")
    parser.add_argument("--confluence-url", help="Confluence instance URL (defaults to jira-url/wiki)")
    parser.add_argument("--email", help="Atlassian account email (or set ATLASSIAN_EMAIL env var)")
    parser.add_argument("--api-token", help="Atlassian API token (or set ATLASSIAN_API_TOKEN env var)")
    parser.add_argument("--days", type=int, default=90, help="Number of days to consider a user inactive (default: 90)")
    parser.add_argument("--output", help="Output file path for text report (optional)")
    parser.add_argument("--csv", help="Output file path for CSV export (optional)")
    parser.add_argument("--date-format", default="%m/%d/%Y %I:%M:%S %p",
                       help="Date format for displaying dates (default: '%%m/%%d/%%Y %%I:%%M:%%S %%p')")
    parser.add_argument("--timezone", default="US/Pacific",
                       help="Timezone for date display (default: US/Pacific)")

    args = parser.parse_args()

    # Get credentials
    email = args.email or os.getenv('ATLASSIAN_EMAIL')
    api_token = args.api_token or os.getenv('ATLASSIAN_API_TOKEN')

    if not email:
        print("Error: Atlassian email is required. Set ATLASSIAN_EMAIL environment variable or use --email")
        sys.exit(1)

    if not api_token:
        print("Error: Atlassian API token is required. Set ATLASSIAN_API_TOKEN environment variable or use --api-token")
        sys.exit(1)

    # Set Confluence URL if not provided
    confluence_url = args.confluence_url
    if not confluence_url:
        confluence_url = args.jira_url.rstrip('/') + '/wiki'

    try:
        # Initialize the finder
        finder = AtlassianInactiveUsersFinder(
            args.jira_url, confluence_url, email, api_token,
            args.days, args.date_format, args.timezone
        )

        # Find inactive users
        inactive_users = finder.find_inactive_users()

        # Generate report
        report = finder.generate_report(inactive_users)

        # Output report
        if args.output:
            with open(args.output, 'w') as f:
                f.write(report)
            print(f"\nText report saved to: {args.output}")
        else:
            print("\n" + "=" * 80)
            print(report)

        # Export to CSV if requested
        if args.csv:
            finder.export_to_csv(inactive_users, args.csv)

    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

# Atlassian (Jira + Confluence) Active Users Finder

A Python application to identify active users across Jira and Confluence instances. This tool helps organization administrators find members who have had activity in the last 3 months across both platforms.

## Features

- 🔍 **Cross-Platform Detection**: Checks activity across both Jira and Confluence
- 📊 **Detailed Reporting**: Generates formatted reports with user information
- 👤 **Rich User Details**: Includes display names, emails, account IDs, and platform access
- 📅 **Actual Last Activity**: Shows real last activity dates across platforms
- 📄 **Multiple Export Formats**: Text reports and CSV exports for easy data handling
- 🚀 **Easy to Use**: Simple command-line interface
- ⚡ **Efficient**: Uses pagination and batch processing to handle large organizations
- 🔒 **Secure**: Uses Atlassian API tokens for authentication

## What Counts as Activity?

The tool considers a user "active" if they have performed any of the following actions within the specified time period (default: 90 days):

**Jira Activity:**
- Created issues
- Updated issues
- Added comments
- Logged work
- Created projects

**Confluence Activity:**
- Created pages or blog posts
- Updated content
- Added comments
- Uploaded attachments

## Prerequisites

- Python 3.7 or higher
- Atlassian API token with appropriate permissions
- Access to both Jira and Confluence instances

## Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up your Atlassian API token**:
   
   Create an Atlassian API token:
   - Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
   - Click "Create API token"
   - Give it a label and copy the generated token

3. **Configure environment variables**:
   ```bash
   cp ../../.env.example .env
   # Edit .env and add your Atlassian credentials
   ```

## Usage

### Basic Usage

```bash
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token
```

### With Custom Confluence URL

```bash
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --confluence-url https://yourcompany.atlassian.net/wiki --email <EMAIL> --api-token your-api-token
```

### With Custom Date Format and Timezone

```bash
# Use custom date format and timezone
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --date-format "%m/%d/%Y %I:%M %p" --timezone "US/Eastern"

# European format with local timezone
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --date-format "%d.%m.%Y %H:%M" --timezone "Europe/Berlin"
```

### Save Report to File

```bash
# Save text report
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --days 90 --output inactive_users_report.txt

# Export to CSV for spreadsheet analysis
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --days 90 --csv inactive_users.csv
```

### Command Line Options

- `--jira-url` (required): Jira instance URL (e.g., https://yourcompany.atlassian.net)
- `--confluence-url` (optional): Confluence instance URL (defaults to jira-url/wiki)
- `--email` (optional): Atlassian account email (can also be set via ATLASSIAN_EMAIL environment variable)
- `--api-token` (optional): Atlassian API token (can also be set via ATLASSIAN_API_TOKEN environment variable)
- `--days` (optional): Number of days to consider a user inactive (default: 90)
- `--output` (optional): Output file path for text report
- `--csv` (optional): Output file path for CSV export
- `--date-format` (optional): Date format for displaying dates (default: "%m/%d/%Y %I:%M:%S %p")
- `--timezone` (optional): Timezone for date display (default: "US/Pacific")

## Example Output

```
Atlassian (Jira + Confluence) Inactive Users Report
Organization: yourcompany
Generated: 2024-06-19 15:30:45
Inactive Period: 90 days
Cutoff Date: 2024-03-19

Total Inactive Users: 3

Inactive Users:
================================================================================
• John Doe (Jira + Confluence)
  Email: <EMAIL>
  Account ID: 5d5f9fbf1234567890abcdef
  Account Status: Active
  Last Activity: 01/15/2024 06:30:22 AM PST (156 days ago)

• Jane Smith (Jira)
  Email: <EMAIL>
  Account ID: 5d5f9fbf0987654321fedcba
  Account Status: Active
  Last Activity: 12/08/2023 01:45:10 AM PST (194 days ago)
```

## Rate Limiting

The Atlassian APIs have rate limits that vary by plan:
- **Free/Standard**: 300 requests per minute
- **Premium/Enterprise**: Higher limits

The script automatically handles these rate limits using a conservative batch processing approach:
- **Batch Processing**: Processes users in batches of 15
- **Automatic Cooldown**: Waits 60 seconds between batches with a countdown timer
- **Rate Limit Prevention**: This approach prevents 429 rate limit errors
- **Progress Indicators**: Shows batch progress and countdown timers

## Security Notes

- Never commit your API token to version control
- Use environment variables or the `.env` file to store sensitive information
- Consider using a token with minimal required permissions
- Regularly rotate your API tokens

## Troubleshooting

### Common Issues

1. **"Error: Atlassian email/API token is required"**
   - Make sure you've set the environment variables or use the command line arguments

2. **"403 Forbidden" errors**
   - Check that your API token has the required permissions
   - Ensure you have access to both Jira and Confluence

3. **"404 Not Found" for Confluence**
   - Verify the Confluence URL is correct
   - Some instances use different URL patterns

4. **Rate limit errors**
   - The script will automatically handle rate limits, but for very large organizations, consider running during off-peak hours

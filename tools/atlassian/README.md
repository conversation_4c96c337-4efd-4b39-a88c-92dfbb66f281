# Atlassian (Jira + Confluence) Inactive Users Finder

A Python application to identify inactive users across Jira and Confluence instances. This tool helps organization administrators find members with active account status who haven't had any activity in the last 3 months across both platforms.

## Features

- 🔍 **Cross-Platform Detection**: Checks activity across both Jira and Confluence
- 📊 **Detailed Reporting**: Generates formatted reports with user information
- 👤 **Rich User Details**: Includes display names, emails, account IDs, and platform access
- 📅 **Actual Last Activity**: Shows real last activity dates across platforms
- 📄 **Multiple Export Formats**: Text reports and CSV exports for easy data handling
- 🚀 **Easy to Use**: Simple command-line interface
- ⚡ **Efficient**: Uses pagination and batch processing to handle large organizations
- 🔒 **Secure**: Uses Atlassian API tokens for authentication

## What Counts as Activity?

The tool first filters to users with `accountStatus: "active"` and valid email addresses (excludes integration accounts), then identifies them as "inactive" if they have NOT performed any of the following actions within the specified time period (default: 90 days):

**Jira Activity:**
- Created issues
- Updated issues
- Added comments
- Logged work
- Created projects

**Confluence Activity:**
- Created pages or blog posts
- Updated content
- Added comments
- Uploaded attachments

## Prerequisites

- Python 3.7 or higher
- Atlassian API token with appropriate permissions
- Access to both Jira and Confluence instances

## Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up your Atlassian API token**:
   
   Create an Atlassian API token:
   - Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
   - Click "Create API token"
   - Give it a label and copy the generated token

3. **Configure environment variables**:
   ```bash
   cp ../../.env.example .env
   # Edit .env and add your Atlassian credentials
   ```

## Usage

### Basic Usage

```bash
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token
```

### With Custom Confluence URL

```bash
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --confluence-url https://yourcompany.atlassian.net/wiki --email <EMAIL> --api-token your-api-token
```

### With Custom Date Format and Timezone

```bash
# Use custom date format and timezone
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --date-format "%m/%d/%Y %I:%M %p" --timezone "US/Eastern"

# European format with local timezone
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --date-format "%d.%m.%Y %H:%M" --timezone "Europe/Berlin"
```

### Save Report to File

```bash
# Save text report
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --days 90 --output inactive_users_report.txt

# Export to CSV for spreadsheet analysis
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token --days 90 --csv inactive_users.csv
```

### Command Line Options

- `--jira-url` (required): Jira instance URL (e.g., https://yourcompany.atlassian.net)
- `--confluence-url` (optional): Confluence instance URL (defaults to jira-url/wiki)
- `--email` (optional): Atlassian account email (can also be set via ATLASSIAN_EMAIL environment variable)
- `--api-token` (optional): Atlassian API token (can also be set via ATLASSIAN_API_TOKEN environment variable)
- `--days` (optional): Number of days to consider a user inactive (default: 90)
- `--output` (optional): Output file path for text report
- `--csv` (optional): Output file path for CSV export
- `--date-format` (optional): Date format for displaying dates (default: "%m/%d/%Y %I:%M:%S %p")
- `--timezone` (optional): Timezone for date display (default: "US/Pacific")

## Example Output

### Console Output During Processing

```
Found 150 total users, 145 with active account status
Checking activity for 145 active account users...
Inactive period: 90 days
Cutoff date: 2024-03-19
Processing in batches of 50 users with 60s breaks to avoid rate limits
--------------------------------------------------------------------------------

📦 Processing batch 1/3 (users 1-50)
[1/145] Checking alice... ✅ Active (last activity: 06/15/2024 02:30:00 AM PDT, 4 days ago)
[2/145] Checking bob... ❌ INACTIVE (last activity: 01/15/2024 06:30:22 AM PST, 156 days ago)
[3/145] Checking charlie... ✅ Active (last activity: 06/10/2024 06:22:00 AM PDT, 9 days ago)
...
[50/145] Checking user50... ✅ Active (last activity: 06/01/2024 01:15:00 AM PDT, 18 days ago)

⏸️  Batch 1 complete. Rate limit cooldown...
⏳ Waiting to avoid rate limits - 01:00 remaining...
⏳ Waiting to avoid rate limits - Complete!

📦 Processing batch 2/3 (users 51-100)
...
```

### Final Report

```
Atlassian (Jira + Confluence) Inactive Users Report
Organization: yourcompany
Generated: 2024-06-19 15:30:45
Inactive Period: 90 days
Cutoff Date: 2024-03-19

Total Inactive Users: 3

Inactive Users:
================================================================================
• Bob Smith (Jira + Confluence)
  Email: <EMAIL>
  Account ID: 5d5f9fbf1234567890abcdef
  Last Activity: 01/15/2024 06:30:22 AM PST (156 days ago)

• Jane Wilson (Jira)
  Email: <EMAIL>
  Account ID: 5d5f9fbf0987654321fedcba
  Last Activity: 12/08/2023 01:45:10 AM PST (194 days ago)

• Old Member (Confluence)
  Email: <EMAIL>
  Account ID: 5d5f9fbf1111222233334444
  Last Activity: Unknown
```

## Rate Limiting

The Atlassian APIs have rate limits that vary by plan:
- **Free/Standard**: 300 requests per minute
- **Premium/Enterprise**: Higher limits

The script automatically handles these rate limits using an optimized batch processing approach:
- **Batch Processing**: Processes users in batches of 50 (optimized for Atlassian's higher rate limits)
- **Automatic Cooldown**: Waits 60 seconds between batches with a countdown timer
- **Rate Limit Prevention**: This approach prevents 429 rate limit errors
- **Progress Indicators**: Shows batch progress and countdown timers

For large organizations, the script will complete faster than GitHub due to larger batch sizes. For example:
- 100 users = ~2 minutes (2 batches)
- 200 users = ~3 minutes (4 batches)
- 500 users = ~10 minutes (10 batches)

## Security Notes

- Never commit your API token to version control
- Use environment variables or the `.env` file to store sensitive information
- Consider using a token with minimal required permissions
- Regularly rotate your API tokens

## Troubleshooting

### Common Issues

1. **"Error: Atlassian email/API token is required"**
   - Make sure you've set the environment variables or use the command line arguments

2. **"403 Forbidden" errors**
   - Check that your API token has the required permissions
   - Ensure you have access to both Jira and Confluence

3. **"404 Not Found" for Confluence**
   - Verify the Confluence URL is correct
   - Some instances use different URL patterns

4. **Rate limit errors**
   - The script will automatically handle rate limits, but for very large organizations, consider running during off-peak hours

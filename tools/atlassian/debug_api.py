#!/usr/bin/env python3
"""
Debug script to test Atlassian API responses and identify the correct structure
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_jira_api(jira_url, email, api_token):
    """Test Jira API endpoints to understand response structure."""
    
    print(f"🔍 Testing Jira API: {jira_url}")
    
    auth = (email, api_token)
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Test different Jira user endpoints
    endpoints = [
        "/rest/api/3/users/search",
        "/rest/api/3/user/search",
        "/rest/api/2/users/search",
        "/rest/api/2/user/search"
    ]
    
    for endpoint in endpoints:
        url = f"{jira_url.rstrip('/')}{endpoint}"
        print(f"\n📡 Testing: {url}")
        
        try:
            # Try with empty query first
            params = {"maxResults": 5}
            response = requests.get(url, auth=auth, headers=headers, params=params)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response type: {type(data)}")
                
                if isinstance(data, list):
                    print(f"List with {len(data)} items")
                    if data:
                        print(f"First item keys: {list(data[0].keys())}")
                        print(f"Sample user: {json.dumps(data[0], indent=2)}")
                elif isinstance(data, dict):
                    print(f"Dict with keys: {list(data.keys())}")
                    print(f"Sample response: {json.dumps(data, indent=2)}")
                
                return True  # Found a working endpoint
                
            else:
                print(f"Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"Exception: {e}")
    
    return False

def test_confluence_api(confluence_url, email, api_token):
    """Test Confluence API endpoints to understand response structure."""
    
    print(f"\n🔍 Testing Confluence API: {confluence_url}")
    
    auth = (email, api_token)
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # Test different Confluence user endpoints
    endpoints = [
        "/rest/api/user",
        "/rest/api/search/user",
        "/rest/api/group/confluence-users/member"
    ]
    
    for endpoint in endpoints:
        url = f"{confluence_url.rstrip('/')}{endpoint}"
        print(f"\n📡 Testing: {url}")
        
        try:
            params = {"limit": 5}
            response = requests.get(url, auth=auth, headers=headers, params=params)
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response type: {type(data)}")
                
                if isinstance(data, list):
                    print(f"List with {len(data)} items")
                    if data:
                        print(f"First item keys: {list(data[0].keys())}")
                        print(f"Sample user: {json.dumps(data[0], indent=2)}")
                elif isinstance(data, dict):
                    print(f"Dict with keys: {list(data.keys())}")
                    if 'results' in data:
                        print(f"Results: {len(data['results'])} items")
                        if data['results']:
                            print(f"First result keys: {list(data['results'][0].keys())}")
                    print(f"Sample response: {json.dumps(data, indent=2)}")
                
                return True  # Found a working endpoint
                
            else:
                print(f"Error: {response.text[:200]}...")
                
        except Exception as e:
            print(f"Exception: {e}")
    
    return False

def main():
    """Main debug function."""
    
    # Get credentials from environment or prompt
    jira_url = input("Enter Jira URL (e.g., https://yourcompany.atlassian.net): ").strip()
    if not jira_url:
        jira_url = "https://yourcompany.atlassian.net"
    
    confluence_url = input(f"Enter Confluence URL (default: {jira_url}/wiki): ").strip()
    if not confluence_url:
        confluence_url = f"{jira_url}/wiki"
    
    email = os.getenv('ATLASSIAN_EMAIL') or input("Enter your Atlassian email: ").strip()
    api_token = os.getenv('ATLASSIAN_API_TOKEN') or input("Enter your API token: ").strip()
    
    if not email or not api_token:
        print("❌ Email and API token are required")
        return
    
    print("🚀 Starting Atlassian API debug session...")
    
    # Test Jira
    jira_success = test_jira_api(jira_url, email, api_token)
    
    # Test Confluence
    confluence_success = test_confluence_api(confluence_url, email, api_token)
    
    print(f"\n📊 Results:")
    print(f"Jira API: {'✅ Working' if jira_success else '❌ Failed'}")
    print(f"Confluence API: {'✅ Working' if confluence_success else '❌ Failed'}")
    
    if not jira_success and not confluence_success:
        print("\n💡 Troubleshooting tips:")
        print("1. Check your Jira/Confluence URLs are correct")
        print("2. Verify your API token has the right permissions")
        print("3. Make sure your email is correct")
        print("4. Try accessing the URLs in a browser first")

if __name__ == "__main__":
    main()

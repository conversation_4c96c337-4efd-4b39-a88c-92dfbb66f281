#!/usr/bin/env python3
"""
Test script to verify the Atlassian tool has the correct batch size settings
"""

import sys
import os

# Add the current directory to the path to import the module
sys.path.append(os.path.dirname(__file__))
from atlassian_inactive_users import AtlassianInactiveUsersFinder

def test_batch_settings():
    """Test that the Atlassian tool has the correct batch settings."""
    
    print("🧪 Testing Atlassian batch settings...")
    
    # Create a test instance
    finder = AtlassianInactiveUsersFinder(
        'https://test.atlassian.net',
        'https://test.atlassian.net/wiki',
        '<EMAIL>',
        'test_token'
    )
    
    # Check batch settings
    expected_batch_size = 50
    expected_wait_time = 60
    
    print(f"Expected batch size: {expected_batch_size}")
    print(f"Actual batch size: {finder.batch_size}")
    print(f"Expected wait time: {expected_wait_time}s")
    print(f"Actual wait time: {finder.batch_wait_time}s")
    
    # Verify settings
    if finder.batch_size == expected_batch_size:
        print("✅ Batch size is correct")
    else:
        print(f"❌ Batch size is incorrect. Expected {expected_batch_size}, got {finder.batch_size}")
        return False
    
    if finder.batch_wait_time == expected_wait_time:
        print("✅ Wait time is correct")
    else:
        print(f"❌ Wait time is incorrect. Expected {expected_wait_time}, got {finder.batch_wait_time}")
        return False
    
    print("\n🎉 All batch settings are correct!")
    print("📊 Performance comparison:")
    print("   GitHub tool: 15 users/batch = slower but safer")
    print("   Atlassian tool: 50 users/batch = faster due to higher rate limits")
    
    return True

if __name__ == "__main__":
    success = test_batch_settings()
    sys.exit(0 if success else 1)

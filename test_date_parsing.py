#!/usr/bin/env python3
"""
Test script to verify date parsing works correctly with different API date formats
"""

import sys
import os

# Add the shared directory to the path
sys.path.append('shared')
from shared.base_audit_tool import BaseAuditTool

def test_date_parsing():
    """Test date parsing with various formats."""
    
    print("🧪 Testing date parsing with different API formats...")
    
    # Create a test instance
    tool = BaseAuditTool()
    
    # Test different date formats
    test_dates = [
        ("2024-06-19T15:30:45Z", "GitHub format"),
        ("2024-06-19T15:30:45.123Z", "GitHub with microseconds"),
        ("2025-04-29T17:05:48.294-0700", "Atlassian format"),
        ("2024-06-19T15:30:45-0700", "With timezone offset"),
        ("2024-06-19T15:30:45", "Basic ISO format"),
        ("invalid-date", "Invalid format"),
    ]
    
    print("\n📅 Testing date formatting:")
    for date_str, description in test_dates:
        try:
            formatted = tool._format_date(date_str)
            days_ago = tool._calculate_days_ago(date_str)
            print(f"✅ {description}:")
            print(f"   Input: {date_str}")
            print(f"   Formatted: {formatted}")
            print(f"   Days ago: {days_ago}")
            print()
        except Exception as e:
            print(f"❌ {description}: Error - {e}")
            print(f"   Input: {date_str}")
            print()
    
    # Test the specific Atlassian format that was causing issues
    atlassian_date = "2025-04-29T17:05:48.294-0700"
    print(f"🎯 Specific test for Atlassian format: {atlassian_date}")
    
    try:
        formatted = tool._format_date(atlassian_date)
        days_ago = tool._calculate_days_ago(atlassian_date)
        
        if days_ago != "unknown":
            print(f"✅ Successfully parsed Atlassian date!")
            print(f"   Formatted: {formatted}")
            print(f"   Days ago: {days_ago}")
            return True
        else:
            print(f"❌ Still showing 'unknown' for days ago")
            return False
            
    except Exception as e:
        print(f"❌ Error parsing Atlassian date: {e}")
        return False

if __name__ == "__main__":
    success = test_date_parsing()
    if success:
        print("\n🎉 Date parsing is working correctly!")
    else:
        print("\n❌ Date parsing needs more work")
    sys.exit(0 if success else 1)

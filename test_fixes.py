#!/usr/bin/env python3
"""
Test script to verify the fixes for date formatting and email filtering
"""

import sys
import os

def test_github_date_formatting():
    """Test that GitHub tool uses consistent date formatting."""
    
    print("🧪 Testing GitHub tool date formatting...")
    
    # Add the GitHub tool directory to the path
    sys.path.append('tools/github')
    from tools.github.github_inactive_users import GitHubInactiveUsersFinder
    
    # Create a test instance
    finder = GitHubInactiveUsersFinder('dummy_token', 'dummy_org')
    
    # Test different GitHub date formats
    test_dates = [
        "2025-06-12T10:20:17.000-07:00",  # The problematic format from your log
        "2025-06-15T22:58:48Z",           # Standard GitHub format
        "2025-04-28T13:48:59.000-07:00",  # Another problematic format
    ]
    
    print("\n📅 Testing GitHub date formatting:")
    all_passed = True
    
    for date_str in test_dates:
        try:
            formatted = finder._format_date(date_str)
            days_ago = finder._calculate_days_ago(date_str)
            
            # Check if it's properly formatted (should not be the raw date string)
            if formatted != date_str and days_ago != "unknown":
                print(f"✅ {date_str}")
                print(f"   Formatted: {formatted}")
                print(f"   Days ago: {days_ago}")
            else:
                print(f"❌ {date_str} - Still showing raw format or unknown")
                all_passed = False
                
        except Exception as e:
            print(f"❌ {date_str} - Error: {e}")
            all_passed = False
    
    return all_passed

def test_atlassian_email_filtering():
    """Test that Atlassian tool filters out users without email."""
    
    print("\n🧪 Testing Atlassian tool email filtering...")
    
    # Add the Atlassian tool directory to the path
    sys.path.append('tools/atlassian')
    from tools.atlassian.atlassian_inactive_users import AtlassianInactiveUsersFinder
    
    # Create a test instance
    finder = AtlassianInactiveUsersFinder(
        'https://test.atlassian.net',
        'https://test.atlassian.net/wiki',
        '<EMAIL>',
        'test_token'
    )
    
    # Create test user data
    test_users = [
        {
            'accountId': 'user1',
            'displayName': 'John Doe',
            'emailAddress': '<EMAIL>',
            'active': True,
            'platforms': {'jira': True, 'confluence': False}
        },
        {
            'accountId': 'user2',
            'displayName': 'Integration Bot',
            'emailAddress': '',  # No email - should be filtered out
            'active': True,
            'platforms': {'jira': True, 'confluence': False}
        },
        {
            'accountId': 'user3',
            'displayName': 'Service Account',
            # No emailAddress field - should be filtered out
            'active': True,
            'platforms': {'jira': True, 'confluence': False}
        },
        {
            'accountId': 'user4',
            'displayName': 'Jane Smith',
            'emailAddress': '<EMAIL>',
            'active': True,
            'platforms': {'jira': True, 'confluence': True}
        },
        {
            'accountId': 'user5',
            'displayName': 'Inactive User',
            'emailAddress': '<EMAIL>',
            'active': False,  # Inactive account - should be filtered out
            'platforms': {'jira': True, 'confluence': False}
        }
    ]
    
    # Test the filtering logic
    active_account_users = [
        user for user in test_users 
        if user.get('active', True) and user.get('emailAddress', '').strip()
    ]
    
    print(f"\n📧 Email filtering test:")
    print(f"   Total test users: {len(test_users)}")
    print(f"   Users after filtering: {len(active_account_users)}")
    
    # Should have 2 users (John Doe and Jane Smith)
    expected_users = 2
    if len(active_account_users) == expected_users:
        print(f"✅ Correctly filtered to {expected_users} users with active status and email")
        
        # Check that the right users are included
        user_names = [user['displayName'] for user in active_account_users]
        if 'John Doe' in user_names and 'Jane Smith' in user_names:
            print("✅ Correct users included in filtered list")
            return True
        else:
            print(f"❌ Wrong users in filtered list: {user_names}")
            return False
    else:
        print(f"❌ Expected {expected_users} users, got {len(active_account_users)}")
        return False

def main():
    """Run all tests."""
    
    print("🔧 Testing fixes for date formatting and email filtering...\n")
    
    # Test GitHub date formatting fix
    github_passed = test_github_date_formatting()
    
    # Test Atlassian email filtering
    atlassian_passed = test_atlassian_email_filtering()
    
    # Summary
    print(f"\n📊 Test Results:")
    print(f"   GitHub date formatting: {'✅ PASSED' if github_passed else '❌ FAILED'}")
    print(f"   Atlassian email filtering: {'✅ PASSED' if atlassian_passed else '❌ FAILED'}")
    
    if github_passed and atlassian_passed:
        print("\n🎉 All fixes are working correctly!")
        return True
    else:
        print("\n❌ Some fixes need more work")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

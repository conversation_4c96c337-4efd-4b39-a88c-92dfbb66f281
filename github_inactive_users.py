#!/usr/bin/env python3
"""
GitHub Inactive Users Finder

This script identifies inactive users in a GitHub organization.
Inactive users are defined as users who haven't had any activity in the last 3 months.
"""

import os
import sys
import requests
import json
import time
import csv
from datetime import datetime, timed<PERSON><PERSON>
from typing import List, Dict, Optional
import argparse
from dotenv import load_dotenv
import pytz

# Load environment variables
load_dotenv()

class GitHubInactiveUsersFinder:
    def __init__(self, token: str, org: str, inactive_days: int = 90,
                 date_format: str = "%Y-%m-%d %H:%M:%S", timezone: str = "UTC"):
        """
        Initialize the GitHub API client.

        Args:
            token: GitHub personal access token
            org: GitHub organization name
            inactive_days: Number of days to consider a user inactive (default: 90 days / 3 months)
            date_format: Date format string for displaying dates (default: "%Y-%m-%d %H:%M:%S")
            timezone: Timezone for date display (default: "UTC")
        """
        self.token = token
        self.org = org
        self.inactive_days = inactive_days
        self.date_format = date_format

        # Set up timezone
        try:
            self.timezone = pytz.timezone(timezone)
        except pytz.UnknownTimeZoneError:
            print(f"Warning: Unknown timezone '{timezone}', falling back to UTC")
            self.timezone = pytz.UTC

        self.base_url = "https://api.github.com"
        self.headers = {
            "Authorization": f"token {token}",
            "Accept": "application/vnd.github.v3+json",
            "User-Agent": "GitHub-Inactive-Users-Finder"
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

        # Calculate the cutoff date based on inactive_days parameter
        self.cutoff_date = datetime.now() - timedelta(days=inactive_days)

        # Rate limiting - batch processing approach
        self.batch_size = 15  # Process 15 users then wait (reduced due to extra API calls for inactive users)
        self.batch_wait_time = 60  # Wait 60 seconds between batches
        
    def _countdown_timer(self, seconds: int, message: str = "Waiting"):
        """
        Display a countdown timer.

        Args:
            seconds: Number of seconds to count down
            message: Message to display during countdown
        """
        for remaining in range(seconds, 0, -1):
            mins, secs = divmod(remaining, 60)
            timer = f"{mins:02d}:{secs:02d}"
            print(f"\r{message} - {timer} remaining...", end="", flush=True)
            time.sleep(1)
        print(f"\r{message} - Complete!           ")  # Clear the line

    def _format_date(self, iso_date_str: str) -> str:
        """
        Format an ISO date string according to the configured format and timezone.

        Args:
            iso_date_str: ISO format date string (e.g., "2024-06-19T15:30:45Z")

        Returns:
            Formatted date string
        """
        try:
            # Parse the ISO date (GitHub API returns UTC dates)
            dt = datetime.strptime(iso_date_str, "%Y-%m-%dT%H:%M:%SZ")
            dt = pytz.UTC.localize(dt)

            # Convert to the configured timezone
            dt_local = dt.astimezone(self.timezone)

            # Format according to the configured format
            formatted = dt_local.strftime(self.date_format)

            # Add timezone abbreviation if not already in format
            if "%Z" not in self.date_format and "%z" not in self.date_format:
                tz_name = dt_local.strftime("%Z")
                formatted += f" {tz_name}"

            return formatted
        except (ValueError, TypeError):
            # Fallback to original string if parsing fails
            return iso_date_str

    def _make_request(self, url: str, params: Optional[Dict] = None) -> Optional[Dict]:
        """
        Make a request to the GitHub API with error handling.

        Args:
            url: API endpoint URL
            params: Query parameters

        Returns:
            JSON response or None if error
        """
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Error making request to {url}: {e}")
            return None
    
    def _get_paginated_results(self, url: str, params: Optional[Dict] = None) -> List[Dict]:
        """
        Get all results from a paginated GitHub API endpoint.

        Args:
            url: API endpoint URL
            params: Query parameters

        Returns:
            List of all results across all pages
        """
        all_results = []
        page = 1
        per_page = 100

        if params is None:
            params = {}

        params.update({"per_page": per_page, "page": page})

        while True:
            response = self._make_request(url, params)
            if not response:
                break

            if isinstance(response, list):
                results = response
            else:
                results = response.get('items', [])

            if not results:
                break

            all_results.extend(results)

            # Check if there are more pages
            if len(results) < per_page:
                break

            page += 1
            params["page"] = page

        return all_results
    
    def get_organization_members(self) -> List[Dict]:
        """
        Get all members of the organization.

        Returns:
            List of organization members
        """
        print(f"Fetching members of organization: {self.org}")
        url = f"{self.base_url}/orgs/{self.org}/members"
        members = self._get_paginated_results(url)
        print(f"Found {len(members)} members in the organization")
        return members

    def get_user_details(self, username: str) -> Dict:
        """
        Get detailed user information including name and email.

        Args:
            username: GitHub username

        Returns:
            Dictionary with user details
        """
        url = f"{self.base_url}/users/{username}"
        user_data = self._make_request(url)

        if user_data:
            return {
                "name": user_data.get("name") or "N/A",
                "email": user_data.get("email") or "N/A",
                "company": user_data.get("company") or "N/A",
                "location": user_data.get("location") or "N/A",
                "bio": user_data.get("bio") or "N/A"
            }
        else:
            return {
                "name": "N/A",
                "email": "N/A",
                "company": "N/A",
                "location": "N/A",
                "bio": "N/A"
            }
    
    def get_last_activity_date(self, username: str) -> Optional[str]:
        """
        Get the actual last activity date for a user, regardless of cutoff date.

        Args:
            username: GitHub username

        Returns:
            ISO date string of last activity or None if no activity found
        """
        last_activity = None

        # Check user events (GitHub returns up to 300 events, newest first)
        events_url = f"{self.base_url}/users/{username}/events"
        events = self._get_paginated_results(events_url)

        if events:
            # Events are returned in chronological order (newest first)
            latest_event = events[0]
            last_activity = latest_event['created_at']

        # Also check for recent commits without date restriction
        search_url = f"{self.base_url}/search/commits"
        search_params = {
            "q": f"author:{username}",
            "sort": "author-date",
            "order": "desc",
            "per_page": 1  # We only need the most recent one
        }

        commits_response = self._make_request(search_url, search_params)
        if commits_response and commits_response.get('total_count', 0) > 0:
            latest_commit = commits_response['items'][0]
            commit_date = latest_commit['commit']['author']['date']

            # Compare with event date and use the more recent one
            if not last_activity:
                last_activity = commit_date
            else:
                event_datetime = datetime.strptime(last_activity, "%Y-%m-%dT%H:%M:%SZ")
                commit_datetime = datetime.strptime(commit_date, "%Y-%m-%dT%H:%M:%SZ")
                if commit_datetime > event_datetime:
                    last_activity = commit_date

        return last_activity

    def get_user_activity(self, username: str) -> Dict:
        """
        Check user activity in the specified time period.

        Args:
            username: GitHub username

        Returns:
            Dictionary with activity information
        """
        activity_info = {
            "username": username,
            "has_recent_activity": False,
            "last_activity_date": None,
            "activity_types": []
        }

        # Check recent events
        events_url = f"{self.base_url}/users/{username}/events"
        events = self._get_paginated_results(events_url)

        recent_events = []
        for event in events:
            event_date = datetime.strptime(event['created_at'], "%Y-%m-%dT%H:%M:%SZ")
            if event_date >= self.cutoff_date:
                recent_events.append(event)
                activity_info["activity_types"].append(event['type'])

        if recent_events:
            activity_info["has_recent_activity"] = True
            # Get the most recent activity date
            latest_event = max(recent_events, key=lambda x: x['created_at'])
            activity_info["last_activity_date"] = latest_event['created_at']

        # Also check recent commits (public repositories)
        # This is an additional check as events might not capture all activity
        # Note: This uses the Search API which has stricter rate limits
        search_url = f"{self.base_url}/search/commits"
        since_date = self.cutoff_date.strftime("%Y-%m-%d")
        search_params = {
            "q": f"author:{username} author-date:>{since_date}",
            "sort": "author-date",
            "order": "desc"
        }

        commits_response = self._make_request(search_url, search_params)
        if commits_response and commits_response.get('total_count', 0) > 0:
            activity_info["has_recent_activity"] = True
            if not activity_info["last_activity_date"]:
                # Use the most recent commit date if no events were found
                latest_commit = commits_response['items'][0]
                activity_info["last_activity_date"] = latest_commit['commit']['author']['date']
            activity_info["activity_types"].append("CommitEvent")

        # If user is inactive, get their actual last activity date
        # Note: This makes additional API calls, which is why we reduced batch_size to 15
        if not activity_info["has_recent_activity"]:
            activity_info["last_activity_date"] = self.get_last_activity_date(username)

        return activity_info
    
    def find_inactive_users(self) -> List[Dict]:
        """
        Find all inactive users in the organization.
        
        Returns:
            List of inactive users with their information
        """
        members = self.get_organization_members()
        inactive_users = []
        
        print(f"\nChecking activity for {len(members)} members...")
        print(f"Inactive period: {self.inactive_days} days")
        print(f"Cutoff date: {self.cutoff_date.strftime('%Y-%m-%d')}")
        print(f"Processing in batches of {self.batch_size} users with {self.batch_wait_time}s breaks to avoid rate limits")
        print("-" * 80)

        # Process users in batches to avoid rate limits
        for batch_start in range(0, len(members), self.batch_size):
            batch_end = min(batch_start + self.batch_size, len(members))
            batch_members = members[batch_start:batch_end]
            batch_num = (batch_start // self.batch_size) + 1
            total_batches = (len(members) + self.batch_size - 1) // self.batch_size

            print(f"\n📦 Processing batch {batch_num}/{total_batches} (users {batch_start + 1}-{batch_end})")

            for i, member in enumerate(batch_members):
                global_index = batch_start + i + 1
                username = member['login']
                print(f"[{global_index}/{len(members)}] Checking {username}...", end=" ")

                activity = self.get_user_activity(username)

                if not activity["has_recent_activity"]:
                    # Get additional user details for inactive users
                    user_details = self.get_user_details(username)

                    inactive_users.append({
                        "username": username,
                        "name": user_details["name"],
                        "email": user_details["email"],
                        "company": user_details["company"],
                        "location": user_details["location"],
                        "profile_url": member['html_url'],
                        "avatar_url": member['avatar_url'],
                        "user_type": member.get('type', 'User'),
                        "last_activity": activity["last_activity_date"]
                    })
                    print("❌ INACTIVE")
                else:
                    print(f"✅ Active (last activity: {activity['last_activity_date']})")

            # Wait between batches (except for the last batch)
            if batch_end < len(members):
                print(f"\n⏸️  Batch {batch_num} complete. Rate limit cooldown...")
                self._countdown_timer(self.batch_wait_time, "⏳ Waiting to avoid rate limits")
        
        return inactive_users
    
    def generate_report(self, inactive_users: List[Dict]) -> str:
        """
        Generate a formatted report of inactive users.
        
        Args:
            inactive_users: List of inactive users
            
        Returns:
            Formatted report string
        """
        report = f"""
GitHub Inactive Users Report
Organization: {self.org}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Inactive Period: {self.inactive_days} days
Cutoff Date: {self.cutoff_date.strftime('%Y-%m-%d')}

Total Inactive Users: {len(inactive_users)}

"""
        
        if inactive_users:
            report += "Inactive Users:\n"
            report += "=" * 80 + "\n"
            for user in inactive_users:
                report += f"• {user['username']} ({user['user_type']})\n"
                report += f"  Name: {user['name']}\n"
                report += f"  Email: {user['email']}\n"
                if user['company'] != 'N/A':
                    report += f"  Company: {user['company']}\n"
                if user['location'] != 'N/A':
                    report += f"  Location: {user['location']}\n"
                report += f"  Profile: {user['profile_url']}\n"

                # Format the last activity date for better readability
                if user['last_activity']:
                    try:
                        # Parse the ISO date and calculate days ago
                        activity_date = datetime.strptime(user['last_activity'], "%Y-%m-%dT%H:%M:%SZ")
                        formatted_date = self._format_date(user['last_activity'])

                        # Calculate how long ago it was
                        days_ago = (datetime.now() - activity_date).days
                        if days_ago == 0:
                            time_ago = "today"
                        elif days_ago == 1:
                            time_ago = "1 day ago"
                        else:
                            time_ago = f"{days_ago} days ago"

                        report += f"  Last Activity: {formatted_date} ({time_ago})\n\n"
                    except ValueError:
                        # Fallback if date parsing fails
                        report += f"  Last Activity: {user['last_activity']}\n\n"
                else:
                    report += f"  Last Activity: Unknown\n\n"
        else:
            report += f"🎉 No inactive users found! All members have been active in the last {self.inactive_days} days.\n"
        
        return report

    def export_to_csv(self, inactive_users: List[Dict], filename: str) -> None:
        """
        Export inactive users to a CSV file.

        Args:
            inactive_users: List of inactive users
            filename: CSV file path
        """
        if not inactive_users:
            print("No inactive users to export.")
            return

        fieldnames = [
            'username', 'name', 'email', 'company', 'location',
            'user_type', 'profile_url', 'last_activity'
        ]

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for user in inactive_users:
                # Format last activity for CSV
                last_activity_formatted = user['last_activity'] or 'Unknown'
                if user['last_activity'] and user['last_activity'] != 'Unknown':
                    try:
                        # Use the configured date format for CSV
                        last_activity_formatted = self._format_date(user['last_activity'])
                    except (ValueError, TypeError):
                        # Keep original format if parsing fails
                        last_activity_formatted = user['last_activity']

                writer.writerow({
                    'username': user['username'],
                    'name': user['name'],
                    'email': user['email'],
                    'company': user['company'],
                    'location': user['location'],
                    'user_type': user['user_type'],
                    'profile_url': user['profile_url'],
                    'last_activity': last_activity_formatted
                })

        print(f"CSV export saved to: {filename}")


def main():
    parser = argparse.ArgumentParser(description="Find inactive users in a GitHub organization")
    parser.add_argument("--org", required=True, help="GitHub organization name")
    parser.add_argument("--token", help="GitHub personal access token (or set GITHUB_TOKEN env var)")
    parser.add_argument("--days", type=int, default=90, help="Number of days to consider a user inactive (default: 90)")
    parser.add_argument("--output", help="Output file path for text report (optional)")
    parser.add_argument("--csv", help="Output file path for CSV export (optional)")
    parser.add_argument("--date-format", default="%Y-%m-%d %H:%M:%S",
                       help="Date format for displaying dates (default: '%%Y-%%m-%%d %%H:%%M:%%S')")
    parser.add_argument("--timezone", default="UTC",
                       help="Timezone for date display (default: UTC). Examples: UTC, US/Eastern, Europe/London, Asia/Tokyo")

    args = parser.parse_args()
    
    # Get GitHub token
    token = args.token or os.getenv('GITHUB_TOKEN')
    if not token:
        print("Error: GitHub token is required. Set GITHUB_TOKEN environment variable or use --token")
        sys.exit(1)
    
    try:
        # Initialize the finder
        finder = GitHubInactiveUsersFinder(token, args.org, args.days,
                                         args.date_format, args.timezone)

        # Find inactive users
        inactive_users = finder.find_inactive_users()
        
        # Generate report
        report = finder.generate_report(inactive_users)
        
        # Output report
        if args.output:
            with open(args.output, 'w') as f:
                f.write(report)
            print(f"\nText report saved to: {args.output}")
        else:
            print("\n" + "=" * 80)
            print(report)

        # Export to CSV if requested
        if args.csv:
            finder.export_to_csv(inactive_users, args.csv)
            
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

# Internal Users Audit Tools

A collection of Python applications to identify inactive users across different platforms. These tools help organization administrators find members who haven't had any activity in a specified time period.

## Available Tools

### 🐙 GitHub Inactive Users Finder
- **Location**: `tools/github/`
- **Purpose**: Identify inactive users in GitHub organizations
- **Features**: Checks GitHub events, commits, and provides detailed activity reports

### 🏢 Atlassian Inactive Users Finder
- **Location**: `tools/atlassian/`
- **Purpose**: Identify inactive users across Jira and Confluence
- **Features**: Cross-platform activity detection, unified user management

## Quick Start

Each tool is self-contained in its own directory with its own README, requirements, and examples.

### GitHub Tool
```bash
cd tools/github
pip install -r requirements.txt
python github_inactive_users.py --org YOUR_ORG_NAME
```

### Atlassian Tool
```bash
cd tools/atlassian
pip install -r requirements.txt
python atlassian_inactive_users.py --jira-url https://yourcompany.atlassian.net --email <EMAIL> --api-token your-api-token
```

## Project Structure

```
internal-users-audit/
├── tools/
│   ├── github/                 # GitHub inactive users tool
│   │   ├── github_inactive_users.py
│   │   ├── example_usage.py
│   │   ├── requirements.txt
│   │   └── README.md
│   └── atlassian/             # Atlassian (Jira + Confluence) tool
│       ├── atlassian_inactive_users.py
│       ├── example_usage.py
│       ├── requirements.txt
│       └── README.md
├── shared/                    # Common utilities
│   └── base_audit_tool.py
├── .env.example              # Environment variables template
├── .gitignore
└── README.md                 # This file
```

## Common Features

All tools share these common features:

- 🔍 **Comprehensive Activity Detection**: Platform-specific activity monitoring
- 📊 **Detailed Reporting**: Generates formatted reports with user information
- 👤 **Rich User Details**: Includes names, emails, and platform-specific information
- 📅 **Configurable Date Formats**: US Pacific time default with customizable formats
- 📄 **Multiple Export Formats**: Text reports and CSV exports for easy data handling
- 🚀 **Easy to Use**: Simple command-line interface
- ⚡ **Efficient**: Uses pagination and batch processing to handle large organizations
- 🔒 **Secure**: Uses platform-specific authentication (API tokens, OAuth)

## Prerequisites

- Python 3.7 or higher
- Platform-specific credentials:
  - **GitHub**: Personal access token with `read:org` and `read:user` scopes
  - **Atlassian**: API token and account email

## Environment Variables

Create a `.env` file in the project root (copy from `.env.example`):

```bash
# GitHub credentials
GITHUB_TOKEN=your_github_token_here

# Atlassian credentials
ATLASSIAN_EMAIL=<EMAIL>
ATLASSIAN_API_TOKEN=your_atlassian_api_token_here
```

## Installation

1. **Clone or download this repository**

2. **Navigate to the specific tool directory**:
   ```bash
   cd tools/github    # For GitHub tool
   # OR
   cd tools/atlassian # For Atlassian tool
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**:
   ```bash
   cp ../../.env.example .env
   # Edit .env and add your credentials
   ```

## Tool-Specific Documentation

Each tool has its own detailed documentation:

- **GitHub Tool**: See `tools/github/README.md` for GitHub-specific setup, usage, and examples
- **Atlassian Tool**: See `tools/atlassian/README.md` for Jira/Confluence setup, usage, and examples

## Common Configuration Options

All tools support these common configuration options:

### Date Format and Timezone

Both tools default to US Pacific time with MM/DD/YYYY format, but can be customized:

```bash
# Use custom date format and timezone
--date-format "%m/%d/%Y %I:%M %p" --timezone "US/Eastern"

# European format with local timezone
--date-format "%d.%m.%Y %H:%M" --timezone "Europe/Berlin"

# ISO format with Tokyo timezone
--date-format "%Y-%m-%dT%H:%M:%S" --timezone "Asia/Tokyo"
```

### Output Options

```bash
# Save text report
--output inactive_users_report.txt

# Export to CSV for spreadsheet analysis
--csv inactive_users.csv

# Generate both text and CSV reports
--output report.txt --csv data.csv
```

### Inactive Period

```bash
# Find users inactive for 60 days or more
--days 60

# Find users inactive for 6 months (180 days) or more
--days 180
```

## Shared Features

### Date Format Options

All tools support configurable date formats using Python strftime format codes:

**Common formats:**
- `"%m/%d/%Y %I:%M:%S %p"` → 06/19/2024 03:30:45 PM (default)
- `"%Y-%m-%d %H:%M:%S"` → 2024-06-19 15:30:45
- `"%d.%m.%Y %H:%M"` → 19.06.2024 15:30

### Timezone Options

**Common timezones:**
- `US/Pacific` - Pacific Time (US) (default)
- `UTC` - Coordinated Universal Time
- `US/Eastern` - Eastern Time (US)
- `Europe/London` - British Time
- `Asia/Tokyo` - Japan Standard Time

### Rate Limiting

All tools use conservative batch processing to handle API rate limits:
- **Batch Processing**: Processes users in batches of 15
- **Automatic Cooldown**: Waits 60 seconds between batches
- **Progress Indicators**: Shows batch progress and countdown timers

## Security Notes

- Never commit API tokens or credentials to version control
- Use environment variables or the `.env` file to store sensitive information
- Consider using tokens with minimal required permissions
- Regularly rotate your access tokens

## Development

### Adding New Tools

To add a new platform audit tool:

1. Create a new directory under `tools/`
2. Extend the `BaseAuditTool` class from `shared/base_audit_tool.py`
3. Implement platform-specific API calls and activity detection
4. Add requirements.txt, README.md, and example_usage.py
5. Update this main README

### Shared Utilities

The `shared/` directory contains common functionality:
- `base_audit_tool.py`: Base class with common features like date formatting, batch processing, and CSV export

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.

## License

This project is open source and available under the MIT License.

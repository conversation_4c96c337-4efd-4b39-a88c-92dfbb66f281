# GitHub Inactive Users Finder

A Python application to identify inactive users in a GitHub organization. This tool helps organization administrators find members who haven't had any activity in the last 3 months.

## Features

- 🔍 **Comprehensive Activity Detection**: Checks both user events and commit activity
- 📊 **Detailed Reporting**: Generates formatted reports with user information
- 👤 **Rich User Details**: Includes full names, emails, company, and location information
- 📅 **Actual Last Activity**: Shows real last activity dates for inactive users (not just "Unknown")
- 📄 **Multiple Export Formats**: Text reports and CSV exports for easy data handling
- 🚀 **Easy to Use**: Simple command-line interface
- ⚡ **Efficient**: Uses pagination to handle large organizations
- 🔒 **Secure**: Uses GitHub personal access tokens for authentication

## What Counts as Activity?

The tool considers a user "active" if they have performed any of the following actions within the specified time period (default: 90 days):

- Created issues or pull requests
- Commented on issues or pull requests
- Pushed commits to any repository
- Starred or watched repositories
- Created or deleted repositories
- Any other GitHub events tracked by the Events API

## Prerequisites

- Python 3.7 or higher
- A GitHub personal access token with appropriate permissions

## Installation

1. **<PERSON>lone or download this repository**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up your GitHub token**:
   
   Create a GitHub personal access token:
   - Go to [GitHub Settings > Developer settings > Personal access tokens](https://github.com/settings/tokens)
   - Click "Generate new token (classic)"
   - Select the following scopes:
     - `read:org` - to read organization membership
     - `read:user` - to read user information
   - Copy the generated token

4. **Configure environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env and add your GitHub token
   ```

## Usage

### Basic Usage

```bash
python github_inactive_users.py --org YOUR_ORG_NAME
```

### With Custom Inactive Period

```bash
# Find users inactive for 60 days or more
python github_inactive_users.py --org YOUR_ORG_NAME --days 60

# Find users inactive for 6 months (180 days) or more
python github_inactive_users.py --org YOUR_ORG_NAME --days 180
```

### With Token as Argument

```bash
python github_inactive_users.py --org YOUR_ORG_NAME --token YOUR_GITHUB_TOKEN --days 30
```

### Save Report to File

```bash
# Save text report
python github_inactive_users.py --org YOUR_ORG_NAME --days 90 --output inactive_users_report.txt

# Export to CSV for spreadsheet analysis
python github_inactive_users.py --org YOUR_ORG_NAME --days 90 --csv inactive_users.csv

# Generate both text and CSV reports
python github_inactive_users.py --org YOUR_ORG_NAME --days 90 --output report.txt --csv data.csv
```

### Custom Date Format and Timezone

```bash
# Use custom date format and timezone
python github_inactive_users.py --org YOUR_ORG_NAME --date-format "%m/%d/%Y %I:%M %p" --timezone "US/Eastern"

# European format with local timezone
python github_inactive_users.py --org YOUR_ORG_NAME --date-format "%d.%m.%Y %H:%M" --timezone "Europe/Berlin"

# ISO format with Tokyo timezone
python github_inactive_users.py --org YOUR_ORG_NAME --date-format "%Y-%m-%dT%H:%M:%S" --timezone "Asia/Tokyo"
```

### Command Line Options

- `--org` (required): GitHub organization name
- `--days` (optional): Number of days to consider a user inactive (default: 90)
- `--token` (optional): GitHub personal access token (can also be set via GITHUB_TOKEN environment variable)
- `--output` (optional): Output file path for text report
- `--csv` (optional): Output file path for CSV export
- `--date-format` (optional): Date format for displaying dates (default: "%m/%d/%Y %I:%M:%S %p")
- `--timezone` (optional): Timezone for date display (default: "US/Pacific")

### Date Format Options

The `--date-format` option accepts Python strftime format codes:

| Format Code | Description | Example |
|-------------|-------------|---------|
| `%Y` | 4-digit year | 2024 |
| `%m` | Month (01-12) | 06 |
| `%d` | Day (01-31) | 19 |
| `%H` | Hour (00-23) | 15 |
| `%M` | Minute (00-59) | 30 |
| `%S` | Second (00-59) | 45 |
| `%I` | Hour (01-12) | 03 |
| `%p` | AM/PM | PM |

**Common formats:**
- `"%Y-%m-%d %H:%M:%S"` → 2024-06-19 15:30:45
- `"%m/%d/%Y %I:%M %p"` → 06/19/2024 03:30 PM
- `"%d.%m.%Y %H:%M"` → 19.06.2024 15:30
- `"%B %d, %Y at %I:%M %p"` → June 19, 2024 at 03:30 PM

### Timezone Options

The `--timezone` option accepts any valid timezone name from the IANA Time Zone Database:

**Common timezones:**
- `UTC` - Coordinated Universal Time
- `US/Eastern` - Eastern Time (US)
- `US/Central` - Central Time (US)
- `US/Mountain` - Mountain Time (US)
- `US/Pacific` - Pacific Time (US)
- `Europe/London` - British Time
- `Europe/Paris` - Central European Time
- `Europe/Berlin` - Central European Time
- `Asia/Tokyo` - Japan Standard Time
- `Asia/Shanghai` - China Standard Time
- `Australia/Sydney` - Australian Eastern Time

## Example Output

```
Fetching members of organization: myorg
Found 25 members in the organization

Checking activity for 25 members...
Inactive period: 90 days
Cutoff date: 2024-03-19
Processing in batches of 15 users with 60s breaks to avoid rate limits
--------------------------------------------------------------------------------

📦 Processing batch 1/2 (users 1-15)
[1/25] Checking alice... ✅ Active (last activity: 2024-06-15T10:30:00Z)
[2/25] Checking bob... ❌ INACTIVE
[3/25] Checking charlie... ✅ Active (last activity: 2024-06-10T14:22:00Z)
...
[15/25] Checking user15... ✅ Active (last activity: 2024-06-01T09:15:00Z)

⏸️  Batch 1 complete. Rate limit cooldown...
⏳ Waiting to avoid rate limits - 01:00 remaining...
⏳ Waiting to avoid rate limits - 00:59 remaining...
...
⏳ Waiting to avoid rate limits - Complete!

📦 Processing batch 2/2 (users 16-25)
[16/25] Checking user16... ❌ INACTIVE
...

============================================================

GitHub Inactive Users Report
Organization: myorg
Generated: 2024-06-19 15:30:45
Inactive Period: 90 days
Cutoff Date: 2024-03-19

Total Inactive Users: 3

Inactive Users:
================================================================================
• bob (User)
  Name: Bob Smith
  Email: <EMAIL>
  Company: Acme Corp
  Location: San Francisco, CA
  Profile: https://github.com/bob
  Last Activity: 2024-01-15 14:30:22 UTC (156 days ago)

• inactive-user (User)
  Name: John Doe
  Email: N/A
  Profile: https://github.com/inactive-user
  Last Activity: 2023-12-08 09:45:10 UTC (194 days ago)

• old-member (User)
  Name: Jane Wilson
  Email: <EMAIL>
  Company: Old Company Inc
  Profile: https://github.com/old-member
  Last Activity: 2023-11-22 16:20:05 UTC (210 days ago)
```

**Note**: The date format and timezone can be customized using the `--date-format` and `--timezone` options. The example above uses the default UTC timezone and "%Y-%m-%d %H:%M:%S" format.
```

## CSV Export Format

When using the `--csv` option, the exported CSV file includes the following columns:

| Column | Description |
|--------|-------------|
| `username` | GitHub username |
| `name` | Full name (if available) |
| `email` | Email address (if public) |
| `company` | Company/organization |
| `location` | Geographic location |
| `user_type` | User or Organization |
| `profile_url` | GitHub profile URL |
| `last_activity` | Date of last activity (formatted according to `--date-format` and `--timezone` options) or "Unknown" |

**Note**: Email addresses are only included if the user has made their email public in their GitHub profile. Many users keep their emails private for privacy reasons.

## Rate Limiting

The GitHub API has rate limits:
- **Authenticated requests**: 5,000 requests per hour
- **Search API**: 30 requests per minute

The script automatically handles these rate limits using a conservative batch processing approach:
- **Batch Processing**: Processes users in batches of 15 (reduced from 20 due to additional API calls for fetching actual last activity dates)
- **Automatic Cooldown**: Waits 60 seconds between batches with a countdown timer
- **Rate Limit Prevention**: This approach prevents 403 rate limit errors
- **Progress Indicators**: Shows batch progress and countdown timers

For large organizations, the script will take time to complete due to these safety measures. For example:
- 45 users = ~3 minutes (3 batches)
- 75 users = ~5 minutes (5 batches)
- 150 users = ~10 minutes (10 batches)

## Troubleshooting

### Common Issues

1. **"Error: GitHub token is required"**
   - Make sure you've set the `GITHUB_TOKEN` environment variable or use the `--token` argument

2. **"403 Forbidden" errors**
   - Check that your token has the required scopes (`read:org`, `read:user`)
   - Ensure you have permission to view the organization's members

3. **"404 Not Found" for organization**
   - Verify the organization name is correct
   - Ensure the organization exists and you have access to it

4. **Rate limit errors**
   - The script will automatically handle rate limits, but for very large organizations, consider running during off-peak hours

### Token Permissions

Your GitHub token needs these permissions:
- `read:org` - Required to list organization members
- `read:user` - Required to access user activity information

## Security Notes

- Never commit your GitHub token to version control
- Use environment variables or the `.env` file to store sensitive information
- Consider using a token with minimal required permissions
- Regularly rotate your access tokens

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this tool.

## License

This project is open source and available under the MIT License.

#!/usr/bin/env python3
"""
Test script to verify all tools can be imported correctly
"""

import sys
import os

def test_imports():
    """Test that all tools can be imported successfully."""
    
    print("🧪 Testing imports for Internal Users Audit Tools...")
    
    # Test shared base class
    try:
        sys.path.append('shared')
        from shared.base_audit_tool import BaseAuditTool
        print("✅ Shared BaseAuditTool imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import BaseAuditTool: {e}")
        return False
    
    # Test GitHub tool
    try:
        sys.path.append('tools/github')
        from tools.github.github_inactive_users import GitHubInactiveUsersFinder
        print("✅ GitHub tool imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import GitHub tool: {e}")
        return False
    
    # Test Atlassian tool
    try:
        sys.path.append('tools/atlassian')
        from tools.atlassian.atlassian_inactive_users import AtlassianInactiveUsersFinder
        print("✅ Atlassian tool imported successfully")
    except ImportError as e:
        print(f"❌ Failed to import Atlassian tool: {e}")
        return False
    
    # Test that tools inherit from base class correctly
    try:
        github_finder = GitHubInactiveUsersFinder('dummy_token', 'dummy_org')
        atlassian_finder = AtlassianInactiveUsersFinder(
            'https://dummy.atlassian.net', 
            'https://dummy.atlassian.net/wiki',
            '<EMAIL>', 
            'dummy_token'
        )
        
        assert isinstance(github_finder, BaseAuditTool)
        assert isinstance(atlassian_finder, BaseAuditTool)
        print("✅ Both tools correctly inherit from BaseAuditTool")
    except Exception as e:
        print(f"❌ Failed to instantiate tools: {e}")
        return False
    
    print("\n🎉 All import tests passed! The project structure is working correctly.")
    return True

if __name__ == "__main__":
    success = test_imports()
    sys.exit(0 if success else 1)

#!/usr/bin/env python3
"""
Base class for user audit tools

This module provides common functionality for auditing inactive users
across different platforms (GitHub, Atlassian, etc.)
"""

import time
import csv
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pytz


class BaseAuditTool:
    """Base class for user audit tools with common functionality."""
    
    def __init__(self, inactive_days: int = 90, 
                 date_format: str = "%m/%d/%Y %I:%M:%S %p", 
                 timezone: str = "US/Pacific"):
        """
        Initialize the base audit tool.

        Args:
            inactive_days: Number of days to consider a user inactive (default: 90)
            date_format: Date format string for displaying dates
            timezone: Timezone for date display
        """
        self.inactive_days = inactive_days
        self.date_format = date_format
        
        # Set up timezone
        try:
            self.timezone = pytz.timezone(timezone)
        except pytz.UnknownTimeZoneError:
            print(f"Warning: Unknown timezone '{timezone}', falling back to UTC")
            self.timezone = pytz.UTC
            
        # Calculate the cutoff date based on inactive_days parameter
        self.cutoff_date = datetime.now() - timedelta(days=inactive_days)

        # Rate limiting - batch processing approach
        self.batch_size = 15  # Process 15 users then wait
        self.batch_wait_time = 60  # Wait 60 seconds between batches

    def _countdown_timer(self, seconds: int, message: str = "Waiting"):
        """
        Display a countdown timer.

        Args:
            seconds: Number of seconds to count down
            message: Message to display during countdown
        """
        for remaining in range(seconds, 0, -1):
            mins, secs = divmod(remaining, 60)
            timer = f"{mins:02d}:{secs:02d}"
            print(f"\r{message} - {timer} remaining...", end="", flush=True)
            time.sleep(1)
        print(f"\r{message} - Complete!           ")  # Clear the line

    def _format_date(self, iso_date_str: str) -> str:
        """
        Format an ISO date string according to the configured format and timezone.
        
        Args:
            iso_date_str: ISO format date string (e.g., "2024-06-19T15:30:45Z")
            
        Returns:
            Formatted date string
        """
        try:
            # Parse the ISO date (APIs typically return UTC dates)
            dt = datetime.strptime(iso_date_str, "%Y-%m-%dT%H:%M:%SZ")
            dt = pytz.UTC.localize(dt)
            
            # Convert to the configured timezone
            dt_local = dt.astimezone(self.timezone)
            
            # Format according to the configured format
            formatted = dt_local.strftime(self.date_format)
            
            # Add timezone abbreviation if not already in format
            if "%Z" not in self.date_format and "%z" not in self.date_format:
                tz_name = dt_local.strftime("%Z")
                formatted += f" {tz_name}"
                
            return formatted
        except (ValueError, TypeError):
            # Fallback to original string if parsing fails
            return iso_date_str

    def _calculate_days_ago(self, iso_date_str: str) -> str:
        """
        Calculate how many days ago a date was.
        
        Args:
            iso_date_str: ISO format date string
            
        Returns:
            Human-readable string like "today", "1 day ago", "5 days ago"
        """
        try:
            activity_date = datetime.strptime(iso_date_str, "%Y-%m-%dT%H:%M:%SZ")
            days_ago = (datetime.now() - activity_date).days
            if days_ago == 0:
                return "today"
            elif days_ago == 1:
                return "1 day ago"
            else:
                return f"{days_ago} days ago"
        except (ValueError, TypeError):
            return "unknown"

    def export_to_csv(self, inactive_users: List[Dict], filename: str, 
                     fieldnames: List[str]) -> None:
        """
        Export inactive users to a CSV file.

        Args:
            inactive_users: List of inactive users
            filename: CSV file path
            fieldnames: List of field names for CSV headers
        """
        if not inactive_users:
            print("No inactive users to export.")
            return

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for user in inactive_users:
                # Format last activity for CSV
                if 'last_activity' in user:
                    last_activity_formatted = user['last_activity'] or 'Unknown'
                    if user['last_activity'] and user['last_activity'] != 'Unknown':
                        try:
                            # Use the configured date format for CSV
                            last_activity_formatted = self._format_date(user['last_activity'])
                        except (ValueError, TypeError):
                            # Keep original format if parsing fails
                            last_activity_formatted = user['last_activity']
                    
                    # Create a copy of user data with formatted date
                    user_data = user.copy()
                    user_data['last_activity'] = last_activity_formatted
                    writer.writerow(user_data)
                else:
                    writer.writerow(user)

        print(f"CSV export saved to: {filename}")

    def generate_base_report_header(self, platform: str, org_name: str, 
                                  total_inactive: int) -> str:
        """
        Generate the header portion of a report.
        
        Args:
            platform: Platform name (e.g., "GitHub", "Atlassian")
            org_name: Organization name
            total_inactive: Number of inactive users
            
        Returns:
            Formatted report header string
        """
        return f"""
{platform} Inactive Users Report
Organization: {org_name}
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
Inactive Period: {self.inactive_days} days
Cutoff Date: {self.cutoff_date.strftime('%Y-%m-%d')}

Total Inactive Users: {total_inactive}

"""
